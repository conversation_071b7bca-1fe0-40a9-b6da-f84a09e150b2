#!/usr/bin/env python3
"""
PhantomCannon Remote API Test Script

This script tests the PhantomCannon remote API endpoints without requiring actual implants.
It demonstrates how to interact with the API and validates that the endpoints are working correctly.

Usage:
1. Start PhantomCannon application
2. Configure and start the API server in the Settings tab
3. Note the API key from the UI
4. Run this script: python3 test_api.py --api-key YOUR_API_KEY --host localhost --port 8080
"""

import argparse
import requests
import json
import sys
import time
from typing import Dict, Any, Optional


class PhantomCannonAPITester:
    def __init__(self, host: str, port: int, api_key: str, use_https: bool = False):
        self.host = host
        self.port = port
        self.api_key = api_key
        self.base_url = f"{'https' if use_https else 'http'}://{host}:{port}"
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
        
    def make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a request to the API and return the response"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, timeout=10)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=self.headers, json=data, timeout=10)
            else:
                return {"error": f"Unsupported method: {method}"}
            
            return {
                "status_code": response.status_code,
                "success": response.status_code < 400,
                "data": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                "headers": dict(response.headers)
            }
        except requests.exceptions.ConnectionError:
            return {"error": "Connection failed - is the API server running?"}
        except requests.exceptions.Timeout:
            return {"error": "Request timed out"}
        except requests.exceptions.RequestException as e:
            return {"error": f"Request failed: {str(e)}"}
        except json.JSONDecodeError:
            return {"error": "Invalid JSON response"}

    def test_status_endpoint(self) -> bool:
        """Test the /api/status endpoint"""
        print("🔍 Testing /api/status endpoint...")
        
        result = self.make_request('GET', '/api/status')
        
        if 'error' in result:
            print(f"❌ Status test failed: {result['error']}")
            return False
            
        if not result['success']:
            print(f"❌ Status test failed: HTTP {result['status_code']}")
            return False
            
        print("✅ Status endpoint working!")
        print(f"   Response: {json.dumps(result['data'], indent=2)}")
        return True

    def test_bots_endpoint(self) -> bool:
        """Test the /api/bots endpoint"""
        print("\n🔍 Testing /api/bots endpoint...")
        
        result = self.make_request('GET', '/api/bots')
        
        if 'error' in result:
            print(f"❌ Bots test failed: {result['error']}")
            return False
            
        if not result['success']:
            print(f"❌ Bots test failed: HTTP {result['status_code']}")
            return False
            
        bots = result['data']
        print(f"✅ Bots endpoint working! Found {len(bots)} bots")
        
        if len(bots) > 0:
            print("   Bot details:")
            for i, bot in enumerate(bots[:3]):  # Show first 3 bots
                print(f"   Bot {i+1}: {bot.get('id', 'Unknown ID')} - {bot.get('hostname', 'Unknown Host')}")
        else:
            print("   No bots currently connected (this is expected for testing)")
            
        return True

    def test_bot_endpoint(self) -> bool:
        """Test the /api/bot/{id} endpoint"""
        print("\n🔍 Testing /api/bot/{id} endpoint...")
        
        # First get the list of bots
        bots_result = self.make_request('GET', '/api/bots')
        if 'error' in bots_result or not bots_result['success']:
            print("⚠️  Skipping bot detail test - no bots available")
            return True
            
        bots = bots_result['data']
        if len(bots) == 0:
            print("⚠️  Skipping bot detail test - no bots available")
            return True
            
        # Test with the first bot
        bot_id = bots[0].get('id')
        if not bot_id:
            print("❌ Bot detail test failed: No bot ID found")
            return False
            
        result = self.make_request('GET', f'/api/bot/{bot_id}')
        
        if 'error' in result:
            print(f"❌ Bot detail test failed: {result['error']}")
            return False
            
        if not result['success']:
            print(f"❌ Bot detail test failed: HTTP {result['status_code']}")
            return False
            
        print(f"✅ Bot detail endpoint working for bot {bot_id}!")
        print(f"   Bot info: {json.dumps(result['data'], indent=2)}")
        return True

    def test_commands_endpoint(self) -> bool:
        """Test the /api/commands endpoint"""
        print("\n🔍 Testing /api/commands endpoint...")
        
        # First get the list of bots
        bots_result = self.make_request('GET', '/api/bots')
        if 'error' in bots_result or not bots_result['success']:
            print("⚠️  Skipping commands test - no bots available")
            return True
            
        bots = bots_result['data']
        if len(bots) == 0:
            print("⚠️  Skipping commands test - no bots available")
            return True
            
        # Test with the first bot
        bot_id = bots[0].get('id')
        if not bot_id:
            print("❌ Commands test failed: No bot ID found")
            return False
            
        result = self.make_request('GET', f'/api/commands?bot_id={bot_id}')
        
        if 'error' in result:
            print(f"❌ Commands test failed: {result['error']}")
            return False
            
        if not result['success']:
            print(f"❌ Commands test failed: HTTP {result['status_code']}")
            return False
            
        commands = result['data']
        print(f"✅ Commands endpoint working! Found {len(commands)} commands for bot {bot_id}")
        
        if len(commands) > 0:
            print("   Recent commands:")
            for i, cmd in enumerate(commands[-3:]):  # Show last 3 commands
                print(f"   Command {i+1}: {cmd.get('command', 'Unknown')} - Status: {cmd.get('status', 'Unknown')}")
        else:
            print("   No commands found for this bot")
            
        return True

    def test_command_endpoint(self) -> bool:
        """Test the /api/command endpoint (POST)"""
        print("\n🔍 Testing /api/command endpoint...")
        
        # First get the list of bots
        bots_result = self.make_request('GET', '/api/bots')
        if 'error' in bots_result or not bots_result['success']:
            print("⚠️  Skipping command send test - no bots available")
            return True
            
        bots = bots_result['data']
        if len(bots) == 0:
            print("⚠️  Skipping command send test - no bots available")
            return True
            
        # Test with the first bot
        bot_id = bots[0].get('id')
        if not bot_id:
            print("❌ Command send test failed: No bot ID found")
            return False
            
        # Send a test command
        command_data = {
            "bot_id": bot_id,
            "command": "info"  # Use the built-in info command
        }
        
        result = self.make_request('POST', '/api/command', command_data)
        
        if 'error' in result:
            print(f"❌ Command send test failed: {result['error']}")
            return False
            
        if not result['success']:
            print(f"❌ Command send test failed: HTTP {result['status_code']}")
            return False
            
        print(f"✅ Command send endpoint working! Sent 'info' command to bot {bot_id}")
        print(f"   Response: {json.dumps(result['data'], indent=2)}")
        return True

    def test_authentication(self) -> bool:
        """Test API authentication"""
        print("\n🔍 Testing API authentication...")
        
        # Test with invalid API key
        invalid_headers = {'X-API-Key': 'invalid_key', 'Content-Type': 'application/json'}
        
        try:
            response = requests.get(f"{self.base_url}/api/status", headers=invalid_headers, timeout=10)
            if response.status_code == 401:
                print("✅ Authentication working! Invalid API key correctly rejected")
                return True
            else:
                print(f"❌ Authentication test failed: Expected 401, got {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Authentication test failed: {str(e)}")
            return False

    def run_all_tests(self) -> bool:
        """Run all API tests"""
        print(f"🚀 Starting PhantomCannon API tests...")
        print(f"   Target: {self.base_url}")
        print(f"   API Key: {self.api_key[:8]}...")
        print("=" * 60)
        
        tests = [
            self.test_authentication,
            self.test_status_endpoint,
            self.test_bots_endpoint,
            self.test_bot_endpoint,
            self.test_commands_endpoint,
            self.test_command_endpoint
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"❌ Test failed with exception: {str(e)}")
        
        print("\n" + "=" * 60)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! The PhantomCannon API is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the API server configuration.")
            
        return passed == total


def main():
    parser = argparse.ArgumentParser(description='Test PhantomCannon Remote API')
    parser.add_argument('--host', default='localhost', help='API server host (default: localhost)')
    parser.add_argument('--port', type=int, default=8080, help='API server port (default: 8080)')
    parser.add_argument('--api-key', required=True, help='API key from PhantomCannon UI')
    parser.add_argument('--https', action='store_true', help='Use HTTPS instead of HTTP')
    
    args = parser.parse_args()
    
    tester = PhantomCannonAPITester(args.host, args.port, args.api_key, args.https)
    
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
