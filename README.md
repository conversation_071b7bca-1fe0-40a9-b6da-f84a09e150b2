# PhantomCannon Project

## About

PhantomCannon is a Command & Control (C2) server with a modern, cyberpunk-inspired UI. It supports Windows implants written in Go and C++, and Linux implants written in Go and C++ that communicate with the C2 server via HTTP/HTTPS.

## Directory Structure

- `implant/`: Legacy Windows implant directory (now uses integrated payload builder)
  - Contains placeholder files - PhantomCannon now uses C++ implants built through the UI
- `linux_nibble/`: Contains the Linux Go implant source code
  - `main.go`: Linux implant with AES-CFB encryption
  - `crypto.go`: Encryption utilities for the Linux implant
  - `persistence.go`: Persistence mechanisms for Linux
  - `system.go`: System information gathering utilities
  - `Makefile`: Build script with support for multiple architectures
- `templates/`: Contains template files for payload generation
  - `windows_implant_c/`: C++ Windows implant templates
  - `linux_main.go.tmpl`: Go Linux implant template
  - Template files are used by the integrated payload builder
- `backend/`: Contains the Go backend code for the C2 server
  - `database.go`: SQLite database implementation
  - `c2server.go`: C2 server implementation
  - `auth.go`: Authentication and user management
  - `network.go`: Network interface detection and management
  - `api.go`: Remote access API implementation
- `frontend/`: Contains the React frontend code
  - Modern UI with cyberpunk theme
  - User authentication and management
  - Implant monitoring and control
  - Integrated payload generation interface
- `PhantomCannonData/Payloads/`: Directory for storing generated payloads (created in current working directory)

## Features

### C2 Server

- Modern, cyberpunk-inspired UI with translucent effects
- User authentication and role-based access control
- User management for administrators
- Secure communication with implants via HTTP/HTTPS
- Direct shell command execution (PowerShell on Windows, Bash on Linux)
- Minimal built-in commands: 'info' for system information, 'ok' for no action
- File upload capabilities
- Detailed logging and monitoring
- Configurable server IP and port (default: 5555)
- Network interface selection for multi-homed systems
- Integrated payload generation for Windows and Linux targets
- Remote access API for controlling bots from separate servers
- Historical bot data archiving with UI management
- Embedded libraries for Linux C++ payload creation

### Windows Implants

#### Go Implant
- Secure communication with C2 server via HTTP
- AES-CFB encryption for secure data exchange
- System information gathering (username, device name, region, memory, OS, network)
- Direct PowerShell command execution
- File upload capability
- Persistence mechanism
- Self-uninstall capability
- Stealth features with random sleep intervals

#### C++ Implant
- Smaller binary size and lower memory footprint
- Secure communication with C2 server via HTTPS
- AES-CFB encryption for secure data exchange
- System information gathering
- Direct PowerShell command execution
- File upload capability
- Multiple persistence mechanisms
- Self-uninstall capability
- Advanced evasion techniques

### Linux Implants

#### Go Implant
- Communication with C2 server via HTTPS
- AES-CFB encryption for secure data exchange
- Detailed system information gathering
- Direct Bash command execution
- File upload capability
- Multiple persistence mechanisms
- Self-uninstall capability
- Configurable sleep intervals
- Comprehensive logging for debugging

#### C++ Implant
- Smaller binary size compared to Go implant
- Lower memory footprint
- AES-CFB encryption for secure data exchange
- System information gathering
- Direct Bash command execution
- File upload capability
- Self-uninstall capability
- Embedded required libraries (no external dependencies)

## Building and Running

### Building the C2 Server

```bash
# For standard build
wails build

# For systems with WebKit2 version 4.1 or higher (recommended for Linux)
wails build -tags webkit2_41
```

The compiled executable will be in the `build/bin` directory.

**Important:** Always use `wails build` instead of `go build` since PhantomCannon is a Wails application.

### Building Linux Implants Manually (Optional)

#### Go Implant

Navigate to the `linux_nibble` directory and use Go to build the implant:

```bash
cd linux_nibble
# Build for current platform
go build -o linux_implant

# Build for specific architectures
make amd64    # AMD64 architecture
make arm64    # ARM64 architecture
make x86      # x86 architecture
make all-arch # All architectures
```

### Using the Integrated Payload Builder (Recommended)

PhantomCannon includes an integrated payload builder in the UI that handles all compilation automatically:

1. Start the PhantomCannon application
2. Go to the Payloads tab
3. Select the target platform (Windows or Linux)
4. Select the implant type:
   - **Windows**: Go or C++
   - **Linux**: Go or C++
5. Enter the C2 server IP and port
6. Click "Build Payload"
7. The generated payload will be saved to the `PhantomCannonData/Payloads/` directory in your current working directory

**Note:** The integrated builder automatically handles:
- Template processing with your server configuration
- Cross-compilation for different architectures
- Embedding required libraries for C++ implants
- Creating build scripts and documentation

## Usage

### C2 Server

1. Run the PhantomCannon executable
2. Log in with the default admin credentials:
   - Username: `admin`
   - Password: `admin`
3. It's recommended to change the default password after first login
4. Configure the server settings in the Settings tab:
   - Select the network interface to bind to
   - Configure the server port (default is 5555)
   - Start the C2 server to begin listening for implant connections

**Note:** The server prevents local machine connections except for IPs in the 10.0.x.x range for security.

### Generating and Deploying Payloads

1. Go to the Payloads tab in the PhantomCannon UI
2. Select the target platform (Windows or Linux)
3. Select the implant type (Go or C++)
4. Enter the C2 server IP and port
5. Click "Build Payload"
6. The generated payload will be saved to the `PhantomCannonData/Payloads/` directory
7. Transfer the payload to the target system and execute it

### Managing Bots

1. Go to the Bots tab in the PhantomCannon UI
2. View all connected bots with their detailed system information:
   - Username, device name, region
   - Memory, OS information, network details
   - Last command and response
3. Select a bot to interact with it:
   - Send direct shell commands (PowerShell on Windows, Bash on Linux)
   - Use built-in commands: `info` (system info) or `ok` (no action)
   - View command history and output
   - Upload files

### Bot Data Management

PhantomCannon maintains historical bot data in an archive:
- Bot data is preserved when the application closes
- Use the UI to view archived bot information
- Clear archived data when needed through the interface

### Remote Access API

1. Go to the Settings tab in the PhantomCannon UI
2. Configure the Remote Access API Server:
   - Select the network interface to bind to from the dropdown
   - You can choose a different network interface than the one used by the C2 server
   - For example, use your internal network interface for the C2 server and your external/public interface for the API server
   - Set the port (must be different from the C2 server port)
   - Start the API server
   - Note the API key for authentication
3. Remote clients can now connect to the API server using the provided API key
4. API endpoints:
   - `GET /api/bots` - List all connected bots
   - `GET /api/bot/{id}` - Get information about a specific bot
   - `POST /api/command` - Send a command to a bot
   - `GET /api/commands?bot_id={id}` - Get command history for a bot
   - `GET /api/status` - Get server status information
5. All API requests must include the `X-API-Key` header with the API key

#### Network Segmentation Example

- C2 Server: Bind to internal network interface (e.g., 192.168.1.10:5000)
  - Implants connect to this interface
  - Keeps C2 traffic isolated to your internal network
- API Server: Bind to external/public network interface (e.g., ************:8080)
  - Remote operators connect to this interface
  - Allows secure remote access without exposing the C2 server directly

### Windows Implants

#### Deployment

1. Transfer the generated Windows payload to the target system
2. Execute the payload:
   ```cmd
   windows_implant.exe
   ```
3. The implant will:
   - Establish persistence automatically
   - Create a self-uninstall script in the execution directory
   - Connect to the C2 server and appear in the Bots tab
   - Execute commands directly in PowerShell

#### Self-Uninstall

Each Windows implant creates an uninstall script that can:
- Remove the implant from the system
- Clean up persistence mechanisms
- Delete itself after execution

### Linux Implants

#### Go Implant

1. Execute the Go Linux implant on the target system:
   ```bash
   chmod +x linux_implant_*
   ./linux_implant_*
   ```
2. The implant will:
   - Establish persistence automatically
   - Create a self-uninstall script
   - Connect to the C2 server and appear in the Bots tab
   - Execute commands directly in Bash
3. Logs are written to `/tmp/implant.log` for debugging

#### C++ Implant

1. Execute the C++ Linux implant on the target system:
   ```bash
   chmod +x linux_implant_cpp_*
   ./linux_implant_cpp_*
   ```
2. The implant will:
   - Run without external dependencies (libraries are embedded)
   - Establish persistence automatically
   - Create a self-uninstall script
   - Connect to the C2 server and appear in the Bots tab
   - Execute commands directly in Bash
3. Logs are written to `/tmp/implant.log` for debugging

**Note:** C++ implants have embedded libraries and don't require separate installation of dependencies.

## Security Features

- Secure password hashing with bcrypt
- Session-based authentication
- Role-based access control (admin and user roles)
- Audit logging for security events
- AES-CFB encryption for secure data exchange
- Configurable encryption keys
- Secure cookie-based session management
- Encrypted command and response transmission
- Local connection filtering (blocks 192.168.x.x range, allows 10.0.x.x range)
- Self-uninstall capabilities for implants
- Embedded libraries to avoid dependency detection

## UI Features

- Cyberpunk-inspired design with teal, pink, and purple accents
- Translucent panels with glass-like effects
- Modern rounded corners and subtle animations
- Dark theme optimized for low-light environments
- Responsive layout for different screen sizes
- Tabbed interface for easy navigation
- Real-time bot status updates
- Command history and output display

## Troubleshooting

### C2 Server

- If the server fails to start, check if the port is already in use
- Ensure you have the correct permissions to bind to the selected port
- For port numbers below 1024, you may need to run as administrator/root
- Check the console output for detailed error messages

### Windows Implants

- Check that the C2 server IP and port are correctly specified in the payload
- Verify that the target system can reach the C2 server
- Some antivirus software may detect and block the implant
- Check Windows Event Logs for execution errors
- Ensure the target system has PowerShell available for command execution

### Linux Implants

#### Go Implant
- Make the implant executable with `chmod +x`
- Check the logs at `/tmp/implant.log` for detailed error messages
- Verify network connectivity to the C2 server
- If the implant doesn't connect, try running with sudo for additional privileges
- Ensure the target system has Bash available for command execution

#### C++ Implant
- Make the implant executable with `chmod +x`
- Check the logs at `/tmp/implant.log` for detailed error messages
- Verify network connectivity to the C2 server
- C++ implants have embedded libraries, so no external dependencies are required
- If connection issues persist, check firewall settings on the target system

### Remote Access API

- If the API server fails to start, check if the port is already in use
- Ensure the API server port is different from the C2 server port
- Verify that remote clients can reach the API server (check firewall settings)
- All API requests must include the `X-API-Key` header with the correct API key
- If you're getting authentication errors, regenerate the API key in the Settings tab
- For network interface issues:
  - Make sure the selected network interface is active and properly configured
  - If binding to a specific interface fails, try using 0.0.0.0 (all interfaces) instead
  - For multi-homed systems, ensure the correct routing is in place
  - Use `ip route` to verify your routing table if remote clients can't connect
  - Consider using port forwarding if your server is behind NAT

## Disclaimer

This tool is for educational purposes only. Use responsibly and only on systems you have permission to access. The authors are not responsible for any misuse or damage caused by this software.
