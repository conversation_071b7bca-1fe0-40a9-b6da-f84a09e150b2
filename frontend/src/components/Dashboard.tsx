import { FC, useEffect, useState } from 'react';
import { Bot } from '@/types';
import { getStatusClass, getStatusText } from '@/lib/utils';

interface DashboardProps {
  bots: Bot[];
  isServerRunning: boolean;
  onStartServer: () => void;
  onStopServer: () => void;
}

const Dashboard: FC<DashboardProps> = ({
  bots,
  isServerRunning,
  onStartServer,
  onStopServer
}) => {
  const [onlineCount, setOnlineCount] = useState(0);
  const [offlineCount, setOfflineCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    if (bots) {
      setOnlineCount(bots.filter(bot => bot.isOnline).length);
      setOfflineCount(bots.filter(bot => !bot.isOnline).length);
      setTotalCount(bots.length);
    }
  }, [bots]);

  return (
    <div className="p-6">
      <h2 className="cyberpunk-header text-2xl mb-6">Dashboard</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="cyberpunk-card">
          <div className="cyberpunk-card-header">
            <h3 className="text-lg font-semibold">Server Status</h3>
          </div>
          <div className="cyberpunk-card-body flex flex-col items-center justify-center">
            <div className="flex items-center mb-4">
              <span className={`inline-block w-4 h-4 rounded-full mr-2 ${isServerRunning ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span className="text-lg">{isServerRunning ? 'Running' : 'Stopped'}</span>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={onStartServer}
                disabled={isServerRunning}
                className={`cyberpunk-button ${isServerRunning ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                Start
              </button>
              <button
                onClick={onStopServer}
                disabled={!isServerRunning}
                className={`cyberpunk-button-secondary ${!isServerRunning ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                Stop
              </button>
            </div>
          </div>
        </div>

        <div className="cyberpunk-card">
          <div className="cyberpunk-card-header">
            <h3 className="text-lg font-semibold">Total Targets</h3>
          </div>
          <div className="cyberpunk-card-body flex items-center justify-center">
            <span className="text-4xl font-bold neon-text-teal">{totalCount}</span>
          </div>
        </div>

        <div className="cyberpunk-card">
          <div className="cyberpunk-card-header">
            <h3 className="text-lg font-semibold">Online Targets</h3>
          </div>
          <div className="cyberpunk-card-body flex items-center justify-center">
            <span className="text-4xl font-bold text-green-500">{onlineCount}</span>
          </div>
        </div>

        <div className="cyberpunk-card">
          <div className="cyberpunk-card-header">
            <h3 className="text-lg font-semibold">Offline Targets</h3>
          </div>
          <div className="cyberpunk-card-body flex items-center justify-center">
            <span className="text-4xl font-bold text-red-500">{offlineCount}</span>
          </div>
        </div>
      </div>

      <div className="cyberpunk-panel">
        <h3 className="cyberpunk-header">Recent Activity</h3>
        {bots && bots.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="cyberpunk-table">
              <thead>
                <tr>
                  <th>Status</th>
                  <th>Target ID</th>
                  <th>IP Address</th>
                  <th>Username</th>
                  <th>Device Name</th>
                  <th>Last Seen</th>
                </tr>
              </thead>
              <tbody>
                {bots.slice(0, 5).map((bot) => (
                  <tr key={bot.id}>
                    <td>
                      <span className={getStatusClass(bot.isOnline)} title={getStatusText(bot.isOnline)}></span>
                    </td>
                    <td className="font-mono">{bot.id.substring(0, 8)}...</td>
                    <td>{bot.publicIP || 'Unknown'}</td>
                    <td>{bot.username || 'Unknown'}</td>
                    <td>{bot.deviceName || 'Unknown'}</td>
                    <td>{new Date(bot.lastSeen).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            No targets connected yet
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
