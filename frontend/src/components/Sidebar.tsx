import { FC, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

// Import Go functions
declare const window: {
  go: {
    main: {
      App: {
        GetCurrentUser: () => Promise<any>;
      };
    };
  };
  location: any;
};

interface SidebarProps {
  className?: string;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const Sidebar: FC<SidebarProps> = ({ className, activeTab, onTabChange }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkUserRole = async () => {
      try {
        const user = await window.go.main.App.GetCurrentUser();
        setIsAdmin(user?.role === 'admin');
      } catch (error) {
        console.error('Failed to get user role:', error);
      } finally {
        setLoading(false);
      }
    };

    checkUserRole();
  }, []);

  const baseTabs = [
    { id: 'dashboard', label: 'Dashboard' },
    { id: 'bots', label: 'Targets' },
    { id: 'archived', label: 'Archived Targets' },
    { id: 'files', label: 'Files' },
    { id: 'payloads', label: 'Payloads' },
    { id: 'settings', label: 'Settings' },
  ];

  const adminTabs = [
    ...baseTabs,
    { id: 'users', label: 'User Management' },
  ];

  const tabs = isAdmin ? adminTabs : baseTabs;

  if (loading) {
    return (
      <aside className={cn('w-64 border-r border-primary bg-black bg-opacity-70', className)}>
        <div className="p-4 flex justify-center">
          <div className="animate-pulse-glow">Loading...</div>
        </div>
      </aside>
    );
  }

  return (
    <aside className={cn('w-64 border-r border-primary bg-black bg-opacity-70', className)}>
      <nav className="p-4">
        <ul className="space-y-2">
          {tabs.map((tab) => (
            <li key={tab.id}>
              <button
                onClick={() => onTabChange(tab.id)}
                className={cn(
                  'w-full text-left px-4 py-2 rounded-lg transition-all duration-300',
                  activeTab === tab.id
                    ? 'cyberpunk-button'
                    : 'text-foreground hover:bg-primary hover:bg-opacity-20'
                )}
              >
                {tab.label}
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;
