import React, { useState, useEffect } from 'react';
import { GetArchivedBots, ClearArchivedBots } from '../../wailsjs/go/main/App';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Table } from './ui/table';
import { Alert } from './ui/alert';

interface Bot {
  id: string;
  firstSeen: string;
  lastSeen: string;
  publicIP: string;
  username: string;
  deviceName: string;
  region: string;
  memory: string;
  netInfo: string;
  osInfo: string;
  processList: string;
  isOnline: boolean;
  lastCommand: string;
  lastResponse: string;
  isArchived: boolean;
}

const ArchivedBots: React.FC = () => {
  const [archivedBots, setArchivedBots] = useState<Bot[]>([]);
  const [selectedBot, setSelectedBot] = useState<Bot | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [showConfirmation, setShowConfirmation] = useState<boolean>(false);
  const [showDetails, setShowDetails] = useState<boolean>(false);

  useEffect(() => {
    refreshArchivedBots();
  }, []);

  const refreshArchivedBots = async () => {
    setLoading(true);
    try {
      const bots = await GetArchivedBots();
      // Type assertion to ensure the bots array matches the Bot interface
      setArchivedBots((bots || []) as Bot[]);
    } catch (error) {
      console.error('Failed to get archived bots:', error);
    } finally {
      setLoading(false);
    }
  };

  const viewBotDetails = (bot: Bot) => {
    setSelectedBot(bot);
    setShowDetails(true);
  };

  const confirmClearArchive = () => {
    setShowConfirmation(true);
  };

  const clearArchive = async () => {
    setShowConfirmation(false);
    setLoading(true);

    try {
      const result = await ClearArchivedBots();
      if (result.success) {
        setArchivedBots([]);
        alert('Archive cleared successfully');
      } else {
        alert('Failed to clear archive: ' + result.message);
      }
    } catch (error) {
      console.error('Failed to clear archive:', error);
      alert('Failed to clear archive: ' + error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string, includeTime = false) => {
    if (!dateString) return 'Unknown';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    if (includeTime) {
      return date.toLocaleString();
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Archived Targets</h2>
        <div className="flex gap-2">
          <Button
            variant="destructive"
            onClick={confirmClearArchive}
            disabled={archivedBots.length === 0}
          >
            Clear Archive
          </Button>
          <Button variant="outline" onClick={refreshArchivedBots}>
            Refresh
          </Button>
        </div>
      </div>

      {loading && (
        <div className="text-center py-4">
          <p>Loading...</p>
        </div>
      )}

      {!loading && archivedBots.length === 0 && (
        <Alert>
          <p className="text-center py-8">No archived targets found.</p>
        </Alert>
      )}

      {!loading && archivedBots.length > 0 && (
        <Card className="overflow-hidden">
          <Table>
            <thead>
              <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Device Name</th>
                <th>IP Address</th>
                <th>OS</th>
                <th>First Seen</th>
                <th>Last Seen</th>
              </tr>
            </thead>
            <tbody>
              {archivedBots.map((bot) => (
                <tr
                  key={bot.id}
                  onClick={() => viewBotDetails(bot)}
                  className="cursor-pointer hover:bg-gray-100"
                >
                  <td>{bot.id.substring(0, 8)}...</td>
                  <td>{bot.username || 'Unknown'}</td>
                  <td>{bot.deviceName || 'Unknown'}</td>
                  <td>{bot.publicIP || 'Unknown'}</td>
                  <td>{bot.osInfo || 'Unknown'}</td>
                  <td>{formatDate(bot.firstSeen)}</td>
                  <td>{formatDate(bot.lastSeen)}</td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Card>
      )}

      {/* Bot Details Modal */}
      {showDetails && selectedBot && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold">Archived Target Details</h3>
              <Button variant="ghost" onClick={() => setShowDetails(false)}>×</Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Basic Information</h4>
                <p><strong>ID:</strong> {selectedBot.id}</p>
                <p><strong>Username:</strong> {selectedBot.username || 'Unknown'}</p>
                <p><strong>Device Name:</strong> {selectedBot.deviceName || 'Unknown'}</p>
                <p><strong>IP Address:</strong> {selectedBot.publicIP || 'Unknown'}</p>
                <p><strong>Region:</strong> {selectedBot.region || 'Unknown'}</p>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Timestamps</h4>
                <p><strong>First Seen:</strong> {formatDate(selectedBot.firstSeen, true)}</p>
                <p><strong>Last Seen:</strong> {formatDate(selectedBot.lastSeen, true)}</p>
              </div>
            </div>

            <div className="mt-4">
              <h4 className="font-semibold mb-2">System Information</h4>
              <p><strong>OS:</strong> {selectedBot.osInfo || 'Unknown'}</p>
              <p><strong>Memory:</strong> {selectedBot.memory || 'Unknown'}</p>
              <p><strong>Network Info:</strong> {selectedBot.netInfo || 'Unknown'}</p>
            </div>

            {selectedBot.processList && (
              <div className="mt-4">
                <h4 className="font-semibold mb-2">Process List</h4>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
                  {selectedBot.processList}
                </pre>
              </div>
            )}

            {(selectedBot.lastCommand || selectedBot.lastResponse) && (
              <div className="mt-4">
                <h4 className="font-semibold mb-2">Last Command</h4>
                <p><strong>Command:</strong> {selectedBot.lastCommand || 'None'}</p>
                <p><strong>Response:</strong></p>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
                  {selectedBot.lastResponse || 'None'}
                </pre>
              </div>
            )}

            <div className="mt-6 flex justify-end">
              <Button onClick={() => setShowDetails(false)}>Close</Button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-xl font-bold mb-4">Confirm Action</h3>
            <p className="mb-6">Are you sure you want to clear all archived targets? This action cannot be undone.</p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowConfirmation(false)}>Cancel</Button>
              <Button variant="destructive" onClick={clearArchive}>Clear Archive</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ArchivedBots;
