import { FC, useState } from 'react';
import { Bot } from '@/types';
import { formatDate, getStatusClass, getStatusText } from '@/lib/utils';
import BotDetail from './BotDetail';

interface BotListProps {
  bots: Bot[];
  onSendCommand: (botId: string, command: string) => void;
  onRefreshBots: () => void;
}

const BotList: FC<BotListProps> = ({ bots, onSendCommand, onRefreshBots }) => {
  const [selectedBot, setSelectedBot] = useState<Bot | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredBots = bots.filter(bot =>
    bot.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (bot.publicIP && bot.publicIP.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (bot.username && bot.username.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (bot.deviceName && bot.deviceName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="cyberpunk-header text-2xl">Targets</h2>
        <div className="flex space-x-4">
          <input
            type="text"
            placeholder="Search targets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="cyberpunk-input"
          />
          <button onClick={onRefreshBots} className="cyberpunk-button">
            Refresh
          </button>
        </div>
      </div>

      {selectedBot ? (
        <div>
          <button
            onClick={() => setSelectedBot(null)}
            className="cyberpunk-button-secondary mb-4"
          >
            Back to List
          </button>
          <BotDetail bot={selectedBot} onSendCommand={onSendCommand} />
        </div>
      ) : (
        <div className="cyberpunk-panel">
          {filteredBots.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="cyberpunk-table">
                <thead>
                  <tr>
                    <th>Status</th>
                    <th>Target ID</th>
                    <th>IP Address</th>
                    <th>Username</th>
                    <th>Device Name</th>
                    <th>OS Info</th>
                    <th>Last Seen</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredBots.map((bot) => (
                    <tr key={bot.id}>
                      <td>
                        <span
                          className={getStatusClass(bot.isOnline)}
                          title={getStatusText(bot.isOnline)}
                        ></span>
                      </td>
                      <td className="font-mono">{bot.id.substring(0, 8)}...</td>
                      <td>{bot.publicIP || 'Unknown'}</td>
                      <td>{bot.username || 'Unknown'}</td>
                      <td>{bot.deviceName || 'Unknown'}</td>
                      <td>{bot.osInfo ? bot.osInfo.substring(0, 20) + '...' : 'Unknown'}</td>
                      <td>{formatDate(bot.lastSeen)}</td>
                      <td>
                        <button
                          onClick={() => setSelectedBot(bot)}
                          className="cyberpunk-button text-xs py-1 px-2"
                        >
                          View
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              {searchTerm ? 'No targets match your search' : 'No targets connected yet'}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BotList;
