import { FC, useState, useEffect } from 'react';
import { Bot, Command, File } from '@/types';
import { formatDate, getStatusClass, getStatusText } from '@/lib/utils';
import CommandTerminal from './CommandTerminal';

// Import Go functions
declare const window: {
  go: {
    main: {
      App: {
        GetCommandsForBot: (botId: string) => Promise<Command[]>;
        GetFilesForBot: (botId: string) => Promise<File[]>;
        SendCommand: (botId: string, command: string) => Promise<boolean>;
      };
    };
  };
  location: any;
};

interface BotDetailProps {
  bot: Bot;
  onSendCommand: (botId: string, command: string) => void;
}

const BotDetail: FC<BotDetailProps> = ({ bot, onSendCommand }) => {
  const [activeTab, setActiveTab] = useState('info');
  const [commands, setCommands] = useState<Command[]>([]);
  const [files, setFiles] = useState<File[]>([]);

  const fetchCommands = async () => {
    try {
      const result = await window.go.main.App.GetCommandsForBot(bot.id);
      setCommands(result || []);
    } catch (error) {
      console.error('Failed to fetch commands:', error);
    }
  };

  const fetchFiles = async () => {
    try {
      const result = await window.go.main.App.GetFilesForBot(bot.id);
      setFiles(result || []);
    } catch (error) {
      console.error('Failed to fetch files:', error);
    }
  };

  useEffect(() => {
    if (activeTab === 'commands') {
      fetchCommands();
    } else if (activeTab === 'files') {
      fetchFiles();
    }
  }, [activeTab, bot.id]);

  return (
    <div className="cyberpunk-panel">
      <div className="flex items-center justify-between mb-6">
        <h2 className="cyberpunk-header text-2xl">Target Details</h2>
        <div className="flex items-center space-x-2">
          <span className={getStatusClass(bot.isOnline)}></span>
          <span>{getStatusText(bot.isOnline)}</span>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex space-x-2 border-b border-primary">
          <button
            className={`px-4 py-2 ${activeTab === 'info' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
            onClick={() => setActiveTab('info')}
          >
            Information
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'commands' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
            onClick={() => setActiveTab('commands')}
          >
            Commands
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'files' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
            onClick={() => setActiveTab('files')}
          >
            Files
          </button>
        </div>
      </div>

      {activeTab === 'info' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold mb-2 neon-text-teal">Basic Information</h3>
            <div className="space-y-2">
              <div className="grid grid-cols-3 gap-2">
                <span className="text-muted-foreground">Target ID:</span>
                <span className="col-span-2 font-mono">{bot.id}</span>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <span className="text-muted-foreground">First Seen:</span>
                <span className="col-span-2">{formatDate(bot.firstSeen)}</span>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <span className="text-muted-foreground">Last Seen:</span>
                <span className="col-span-2">{formatDate(bot.lastSeen)}</span>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <span className="text-muted-foreground">Public IP:</span>
                <span className="col-span-2">{bot.publicIP || 'Unknown'}</span>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <span className="text-muted-foreground">Username:</span>
                <span className="col-span-2">{bot.username || 'Unknown'}</span>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <span className="text-muted-foreground">Device Name:</span>
                <span className="col-span-2">{bot.deviceName || 'Unknown'}</span>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <span className="text-muted-foreground">Region:</span>
                <span className="col-span-2">{bot.region || 'Unknown'}</span>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <span className="text-muted-foreground">Memory:</span>
                <span className="col-span-2">{bot.memory || 'Unknown'}</span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2 neon-text-teal">System Information</h3>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-semibold text-muted-foreground mb-1">OS Info</h4>
                <div className="bg-black bg-opacity-50 p-2 rounded-sm font-mono text-xs whitespace-pre-wrap max-h-32 overflow-y-auto">
                  {bot.osInfo || 'No OS information available'}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-semibold text-muted-foreground mb-1">Network Info</h4>
                <div className="bg-black bg-opacity-50 p-2 rounded-sm font-mono text-xs whitespace-pre-wrap max-h-32 overflow-y-auto">
                  {bot.netInfo || 'No network information available'}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-semibold text-muted-foreground mb-1">Last Command</h4>
                <div className="bg-black bg-opacity-50 p-2 rounded-sm font-mono text-xs whitespace-pre-wrap max-h-32 overflow-y-auto">
                  {bot.lastCommand || 'No command history'}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-semibold text-muted-foreground mb-1">Last Response</h4>
                <div className="bg-black bg-opacity-50 p-2 rounded-sm font-mono text-xs whitespace-pre-wrap max-h-32 overflow-y-auto">
                  {bot.lastResponse || 'No response history'}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'commands' && (
        <CommandTerminal
          botId={bot.id}
          commands={commands}
          onSendCommand={onSendCommand}
          onRefreshCommands={fetchCommands}
        />
      )}

      {activeTab === 'files' && (
        <div>
          <h3 className="text-lg font-semibold mb-2 neon-text-teal">Uploaded Files</h3>
          {files.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="cyberpunk-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Filename</th>
                    <th>Size</th>
                    <th>Timestamp</th>
                    <th>Path</th>
                  </tr>
                </thead>
                <tbody>
                  {files.map((file) => (
                    <tr key={file.id}>
                      <td>{file.id}</td>
                      <td>{file.filename}</td>
                      <td>{file.size} bytes</td>
                      <td>{formatDate(file.timestamp)}</td>
                      <td className="font-mono text-xs">{file.path}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              No files have been uploaded from this bot
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BotDetail;
